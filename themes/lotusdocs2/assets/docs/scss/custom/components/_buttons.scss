//
// _buttons.scss
//

:root {
    --invert: invert(0%);
    --btn-modal-hover-bg: var(--gray-200);
}

[data-dark-mode] {
    --invert: invert(100%);
    --btn-modal-hover-bg: var(--gray-800);
}

//Buttons
.btn-soft {
    background-color: var(--btn-soft-bg) !important;
    border: 1px solid var(--btn-soft-border) !important;
    color: var(--btn-soft-color) !important;
    // box-shadow: 0 3px 5px 0 hsl(var(--primary-hsl), 0.1);

    &:hover,
    // &:focus,
    &:active,
    &.active,
    &.focus {
        // background-color: var(--primary) !important;
        background: var(--btn-soft-bg);
        border-color: var(--btn-soft-border) !important;
        color: var(--btn-soft-color) !important;
    }
}

.btn-primary {
    background-color: var(--btn-primary-bg) !important;
    border: 1px solid var(--btn-primary-border) !important;
    color: var(--btn-primary-color) !important;
    // box-shadow: 0 3px 5px 0 hsl(var(--primary-hsl), 0.1);

    &:hover,
    // &:focus,
    &:active,
    &.active,
    &.focus {
        // background-color: var(--primary) !important;
        background: var(--btn-primary-bg);
        border-color: var(--btn-primary-border) !important;
        color: var(--btn-primary-color) !important;
    }
}

.btn-default {
    background-color: var(--btn-default-bg) !important;
    border: 1px solid var(--btn-default-border) !important;
    color: var(--btn-default-color) !important;
    // box-shadow: 0 3px 5px 0 hsl(var(--primary-hsl), 0.1);

    &:hover,
    // &:focus,
    &:active,
    &.active,
    &.focus {
        // background-color: var(--primary) !important;
        background: var(--btn-default-bg);
        border-color: var(--btn-default-border) !important;
        color: var(--btn-default-hover-color) !important;
    }
}

.btn-link-modal {
    --bs-btn-font-weight: 600;
    --bs-btn-color: var(--text-default);
    --bs-btn-bg: none;
    --bs-btn-border-color: var(--bd-violet-bg);
    --bs-btn-hover-color: var(--btn-default-hover-color);
    --bs-btn-hover-bg: var(--btn-modal-hover-bg);
    --bs-btn-hover-border-color: var(--bd-violet-bg);
    --bs-btn-focus-shadow-rgb: var(--bd-violet-rgb);
    --bs-btn-active-color: var(--bs-btn-hover-color);
    --bs-btn-active-bg: var(--bd-violet-bg);
    --bs-btn-active-border-color: var(--bd-violet-bg);
}

.btn-close {
    filter: var(--invert);
}

body .toggle-dark {
    display: block;
}

body .toggle-light {
    display: none;
}

[data-dark-mode] body .toggle-light {
    display: block;
}

[data-dark-mode] body .toggle-dark {
    display: none;
}

.btn {
    padding: 8px 20px;
    outline: none;
    text-decoration: none;
    font-size: 16px;
    letter-spacing: 0.5px;
    transition: all 0.3s;
    font-weight: 600;
    border-radius: 6px;

    &:focus {
        box-shadow: none !important;
    }

    &.btn-sm {
        padding: 7px 16px;
        font-size: 12px;
    }

    &.btn-xs {
        padding: 4px 10px;
        font-size: 10px;
    }

    &.btn-lg {
        padding: 14px 30px;
        font-size: 16px;
    }

    &.searchbtn {
        padding: 6px 20px;
    }

    &.btn-pills {
        border-radius: 30px;
    }

    // &.btn-light {
    //     border: 1px solid darken($light, 2%);
    // }

    &.btn-outline-light {
        border-color: var(--gray-200) !important;
    }

    &.btn-soft-light {
        color: var(--gray-500) !important;
        border-color: var(--gray-200) !important;
    }

    &.btn-soft-dark {

        &:hover,
        &:focus,
        &:active,
        &.active,
        &.focus {
            color: var(--gray-400) !important;
            border-color: var(--gray-200) !important;
        }
    }

    &.btn-dark,
    &.btn-secondary {
        color: var(--gray-200);
    }

    &.btn-outline-light {
        color: var(--gray-900);
    }

    &.btn-icon {
        height: 36px;
        width: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        .icons {
            height: 16px;
            width: 16px;
            font-size: 16px;
        }

        &.btn-lg {
            height: 48px;
            width: 48px;
            line-height: 46px;

            .icons {
                height: 20px;
                width: 20px;
                font-size: 20px;
            }
        }

        &.btn-sm {
            height: 30px;
            width: 30px;
            line-height: 28px;
        }
    }
}

button:not(:disabled) {
    outline: none;
}