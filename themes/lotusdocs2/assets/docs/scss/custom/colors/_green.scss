// green
:root {
    --primary: var(--green);
    --primary-50: var(--green-50);
    --primary-100: var(--green-100);
    --primary-200: var(--green-200);
    --primary-300: var(--green-300);
    --primary-400: var(--green-400);
    --primary-800: var(--green-800);

    --primary-hsl: var(--green-500-hsl);
    --primary-50-hsl: var(--green-50-hsl);
    --primary-100-hsl: var(--green-100-hsl);
    --primary-200-hsl: var(--green-200-hsl);
    --primary-300-hsl: var(--green-300-hsl);
    --primary-800-hsl: var(--green-800-hsl);
}