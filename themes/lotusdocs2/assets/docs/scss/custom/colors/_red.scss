// red
:root {
    --primary: var(--red);
    --primary-50: var(--red-50);
    --primary-100: var(--red-100);
    --primary-200: var(--red-200);
    --primary-300: var(--red-300);
    --primary-400: var(--red-400);
    --primary-800: var(--red-800);

    --primary-hsl: var(--red-500-hsl);
    --primary-50-hsl: var(--red-50-hsl);
    --primary-100-hsl: var(--red-100-hsl);
    --primary-200-hsl: var(--red-200-hsl);
    --primary-300-hsl: var(--red-300-hsl);
    --primary-800-hsl: var(--red-800-hsl);
}