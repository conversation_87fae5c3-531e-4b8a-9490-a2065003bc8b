// emerald
:root {
    --primary: var(--emerald);
    --primary-50: var(--emerald-50);
    --primary-100: var(--emerald-100);
    --primary-200: var(--emerald-200);
    --primary-300: var(--emerald-300);
    --primary-400: var(--emerald-400);
    --primary-800: var(--emerald-800);

    --primary-hsl: var(--emerald-500-hsl);
    --primary-50-hsl: var(--emerald-50-hsl);
    --primary-100-hsl: var(--emerald-100-hsl);
    --primary-200-hsl: var(--emerald-200-hsl);
    --primary-300-hsl: var(--emerald-300-hsl);
    --primary-800-hsl: var(--emerald-800-hsl);
}