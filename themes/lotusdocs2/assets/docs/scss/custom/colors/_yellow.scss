// yellow
:root {
    --primary: var(--yellow);
    --primary-50: var(--yellow-50);
    --primary-100: var(--yellow-100);
    --primary-200: var(--yellow-200);
    --primary-300: var(--yellow-300);
    --primary-400: var(--yellow-400);
    --primary-800: var(--yellow-800);

    --primary-hsl: var(--yellow-500-hsl);
    --primary-50-hsl: var(--yellow-50-hsl);
    --primary-100-hsl: var(--yellow-100-hsl);
    --primary-200-hsl: var(--yellow-200-hsl);
    --primary-300-hsl: var(--yellow-300-hsl);
    --primary-800-hsl: var(--yellow-800-hsl);
}