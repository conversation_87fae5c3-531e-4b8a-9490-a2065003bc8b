// cardinal
:root {
    --primary: var(--cardinal);
    --primary-50: var(--cardinal-50);
    --primary-100: var(--cardinal-100);
    --primary-200: var(--cardinal-200);
    --primary-300: var(--cardinal-300);
    --primary-400: var(--cardinal-400);
    --primary-800: var(--cardinal-800);

    --primary-hsl: var(--cardinal-500-hsl);
    --primary-50-hsl: var(--cardinal-50-hsl);
    --primary-100-hsl: var(--cardinal-100-hsl);
    --primary-200-hsl: var(--cardinal-200-hsl);
    --primary-300-hsl: var(--cardinal-300-hsl);
    --primary-800-hsl: var(--cardinal-800-hsl);
}