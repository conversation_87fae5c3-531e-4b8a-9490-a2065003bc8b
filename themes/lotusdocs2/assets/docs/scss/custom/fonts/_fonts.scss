
/* nunito-300 - latin */
@font-face {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/nunito-v25-latin-300.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/nunito-v25-latin-300.woff') format('woff'), /* Modern Browsers */
}

/* nunito-regular - latin */
@font-face {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/nunito-v25-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/nunito-v25-latin-regular.woff') format('woff'), /* Modern Browsers */
}

/* nunito-500 - latin */
@font-face {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/nunito-v25-latin-500.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/nunito-v25-latin-500.woff') format('woff'), /* Modern Browsers */
}

/* nunito-600 - latin */
@font-face {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/nunito-v25-latin-600.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/nunito-v25-latin-600.woff') format('woff'), /* Modern Browsers */
}

/* nunito-700 - latin */
@font-face {
  font-family: 'Nunito';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/nunito-v25-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/nunito-v25-latin-700.woff') format('woff'), /* Modern Browsers */
}

/* source-code-pro-500 - latin */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/source-code-pro-v22-latin-500.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/source-code-pro-v22-latin-500.woff') format('woff'), /* Modern Browsers */
}

/* source-code-pro-700 - latin */
@font-face {
  font-family: 'Source Code Pro';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local(''),
       url('/docs/fonts/source-code-pro-v22-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
      //  url('/docs/fonts/source-code-pro-v22-latin-700.woff') format('woff'), /* Modern Browsers */
}