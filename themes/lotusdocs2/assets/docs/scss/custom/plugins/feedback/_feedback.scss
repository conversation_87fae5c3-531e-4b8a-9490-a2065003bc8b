/* Feedback Widget */

:root {
    --emoticon-container-border-color: var(--gray-500);
    --emoticon-color: var(--gray-500);
    --emoticon-checked-color: var(--primary);
    --emoticon-checked--bg-color: var(--primary-100);
}

[data-dark-mode] {
    --emoticon-container-border-color: var(--gray-700);
    --emoticon-color: var(--gray-600);
    --emoticon-checked-color: var(--primary-300);
    --emoticon-checked--bg-color: var(--gray-800);
}

#feedback-init {
    transition: opacity 0.5s;
}

#feedback-form-pos,
#feedback-form-neg,
#feedback-end,
#feedback-emoji-end,
#feedback-submit-container,
#feedback-emoji-submit-container {
    height: 0;
    line-height:0;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.5s;

    &.is-visible {
        height: auto;
        line-height: inherit;
        overflow: inherit;
        opacity: 1;
    }

    .form-check {
        margin-bottom: 0.525rem;
    }

    .form-check-label,
    .form-check-input {
        font-weight: 600;
        cursor: pointer;
    }
}

#feedback-emoji-end {
    padding: 0;

    &.is-visible {
        padding: 10px 30px;
        width: 40vh;
    }
}

.feedback-radio-desc {
    color: var(--text-muted);
    font-size: 14px;
}

.feedback-textarea {
    width: 50%;
    font-size: 0.875rem;
    color: var(--text-default);
    background: var(--card-bg);
    resize: none;
    border: solid 1px;
    border-radius: 4px    ;
    border-color: var(--card-border-color);
    @media (max-width:578px) {
        width: 100%;
    }
}

#feedback-widget-emoticon {
    border: 1px solid var(--emoticon-container-border-color);
    border-radius: 30px;
    padding: 1px 5px;
    width: min-content;
}

#feedback-cta {
    width: max-content;
}

#text-wrapper {

    height: 0;
    line-height:0;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.5s;

    &.is-visible {
        height: auto;
        line-height: inherit;
        overflow: inherit;
        opacity: 1;

        padding: 10px 3px 0px 3px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
}

.feedback-textarea-emoji {
    width: 100%;
    font-size: 0.875rem;
    color: var(--text-default);
    background: var(--card-bg);
    resize: none;
    border: solid 1px;
    border-radius: 4px    ;
    border-color: var(--card-border-color);
}

.radio-emoji {

    >input[type="radio"] {
    display: none;

        +label {
            background: transparent;
            color: var(--emoticon-color);
            height: 32px;
            width: 32px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition:background .2s,border-color .2s;
        }
    }
}

.radio-emoji input[type="radio"]:checked + label {
    background: var(--emoticon-checked--bg-color);
    color: var(--emoticon-checked-color);
}