/*! @docsearch/css Button 3.3.0 | MIT License | © Algolia, Inc. and contributors | https://docsearch.algolia.com */

.DocSearch-Button {
    align-items: center;
    background: var(--docsearch-searchbox-background);
    border: 0;
    border-radius: 40px;
    color: var(--docsearch-muted-color);
    cursor: pointer;
    display: flex;
    font-weight: 500;
    height: 36px;
    justify-content: space-between;
    margin: 0 0 0 16px;
    padding: 0 8px;
    user-select: none
}

.DocSearch-Button:active,
.DocSearch-Button:focus,
.DocSearch-Button:hover {
    background: var(--docsearch-searchbox-focus-background);
    box-shadow: var(--docsearch-searchbox-shadow);
    color: var(--docsearch-text-color);
    outline: none
}

.DocSearch-Button-Container {
    align-items: center;
    display: flex
}

.DocSearch-Search-Icon {
    stroke-width: 1.6
}

.DocSearch-Button .DocSearch-Search-Icon {
    color: var(--docsearch-text-color)
}

.DocSearch-<PERSON>ton-Placeholder {
    font-size: 1rem;
    padding: 0 12px 0 6px
}

.DocSearch-Button-Keys {
    display: flex;
    min-width: calc(40px + .8em)
}

.DocSearch-Button-Key {
    align-items: center;
    background: var(--docsearch-key-gradient);
    border-radius: 3px;
    box-shadow: var(--docsearch-key-shadow);
    color: var(--docsearch-muted-color);
    display: flex;
    height: 18px;
    justify-content: center;
    margin-right: .4em;
    position: relative;
    padding: 0 0 2px;
    border: 0;
    top: -1px;
    width: 20px
}

@media (max-width:768px) {

    .DocSearch-Button-Keys,
    .DocSearch-Button-Placeholder {
        display: none
    }
}