/* PrismJS 1.29.0 */

// Fit codeblock container to contents
// .prism-codeblock {
//     width: fit-content;
//     max-width: 100%;
// }

code {
    color: var(--text-default);
}

.docs-content .main-content pre {
    background-color: var(--prism-code-bg) !important;
}

code[class*="language-"],
pre[class*="language-"] {
    border: none !important;
    font-family: $font-family-monospace;
    font-size: 0.8rem;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;

    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;

    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    scrollbar-width: thin;
    scrollbar-color: var(--prism-code-scrollbar-thumb-color) var(--prism-code-bg);

    &::-webkit-scrollbar {
        height: 5px;
        background: var(--prism-code-bg);
    }

    &::-webkit-scrollbar-thumb {
        background: var(--prism-code-scrollbar-thumb-color);
    }
}

pre[data-line] {
     position: relative;
     padding: 0 !important;
 }

 .line-highlight:first-of-type {
    margin-top: 0em;
 }

 .line-highlight:last-of-type {
    margin-top: 0em !important;
 }

 .line-highlight {
     position: absolute;
     left: 0;
     right: 0;
     padding: inherit 0;
     margin-top: 0em; /* Same as .prism’s padding-top */

     background: hsla(24, 20%, 50%,.08);
     background: linear-gradient(to right, hsla(24, 20%, 50%,.1) 70%, hsla(24, 20%, 50%, 0));

     pointer-events: none;

     line-height: inherit;
     white-space: pre;
 }

 @media print {
     .line-highlight {
         /*
          * This will prevent browsers from replacing the background color with white.
          * It's necessary because the element is layered on top of the displayed code.
          */
         -webkit-print-color-adjust: exact;
         color-adjust: exact;
     }
 }

     .line-highlight:before,
     .line-highlight[data-end]:after {
         content: attr(data-start);
         position: absolute;
         top: 0.14em;
         left: .6em;
         min-width: 1.5em;
         padding: 0 .5em;
        //  background-color: var(--blue-400);
         color: hsl(24, 20%, 95%);
         font: bold 95%/1.3 sans-serif;
         text-align: center;
         vertical-align: .3em;
         border-radius: 4px;
         text-shadow: none;
        //  box-shadow: 0 1px white;
     }

     .line-highlight[data-end]:after {
         content: attr(data-end);
         top: auto;
         bottom: .14em;
     }

 .line-numbers .line-highlight {
    margin-top: 0em !important;
 }

 .line-numbers .line-highlight:before,
 .line-numbers .line-highlight:after {
     content: none;
 }

//  .line-numbers .line-highlight:first-of-type,
//  .line-numbers .line-highlight:last-of-type {
//     margin-top: 0 !important;
//  }

 pre[id].linkable-line-numbers span.line-numbers-rows {
     pointer-events: all;
 }
 pre[id].linkable-line-numbers span.line-numbers-rows > span:before {
     cursor: pointer;
 }
 pre[id].linkable-line-numbers span.line-numbers-rows > span:hover:before {
     background-color: rgba(128, 128, 128, .2);
 }

 pre[class*="language-"].line-numbers {
     position: relative;
     padding-left: 0;
     counter-reset: linenumber;
 }

 pre[class*="language-"].line-numbers > code {
     position: relative;
     white-space: inherit;
     padding: 0.3rem 3.0rem 1.25rem 3rem !important;
 }

 .line-numbers .line-numbers-rows {
     position: absolute;
     pointer-events: none;
     top: .40em;
     font-size: 100%;
     left: 0em;
     width: 3em; /* works for line-numbers below 1000 lines */
     letter-spacing: -1px;
    //  border-right: 1px solid #999;
     border-right: none;

     -webkit-user-select: none;
     -moz-user-select: none;
     -ms-user-select: none;
     user-select: none;

 }

     .line-numbers-rows > span {
         display: block;
         counter-increment: linenumber;
     }

         .line-numbers-rows > span:before {
             content: counter(linenumber);
             color: var(--gray-400);
             display: block;
             padding-right: 0.8em;
             text-align: right;
         }

 div.code-toolbar {
     position: relative;
     margin: 16px 0;
     padding-top: 1.25rem !important;
     background-color: var(--prism-code-bg);
     border-radius: 4px;
 }

 div.prism-codeblock.hl_lines .code-toolbar {
    padding-top: 1.25rem !important;
 }

 div.prism-shortcode.data-line .code-toolbar {
    padding-top: 1.25rem !important;
 }

 div.code-toolbar > .toolbar {
     position: absolute;
     z-index: 10;
     top: .4em;
     right: .4em;
     transition: opacity 0.3s ease-in-out;
     opacity: 0;
 }

 div.code-toolbar:hover > .toolbar {
     opacity: 1;
 }

 /* Separate line b/c rules are thrown out if selector is invalid.
    IE11 and old Edge versions don't support :focus-within. */
 div.code-toolbar:focus-within > .toolbar {
     opacity: 1;
 }

 div.code-toolbar > .toolbar > .toolbar-item {
     display: inline-block;
 }

 div.code-toolbar > .toolbar > .toolbar-item > a {
     cursor: pointer;
 }

 div.code-toolbar > .toolbar > .toolbar-item > button {
     background: none;
     border: 0;
     color: inherit;
     font: inherit;
     line-height: normal;
     overflow: visible;
     padding: 0;
     -webkit-user-select: none; /* for button */
     -moz-user-select: none;
     -ms-user-select: none;

     &.copy-to-clipboard-button {
        box-shadow: none;
        background: var(--prism-code-bg);
        opacity: 0.9;
     }
 }

 // Custom Copy, Copied and Error Icons
 [data-copy-state="copy"] span:empty::before {
     content: "";
     -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M9 43.95q-1.2 0-2.1-.9-.9-.9-.9-2.1V10.8h3v30.15h23.7v3Zm6-6q-1.2 0-2.1-.9-.9-.9-.9-2.1v-28q0-1.2.9-2.1.9-.9 2.1-.9h22q1.2 0 *******.9.9 2.1v28q0 1.2-.9 2.1-.9.9-2.1.9Zm0-3h22v-28H15v28Zm0 0v-28 28Z'/%3E%3C/svg%3E");
     mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M9 43.95q-1.2 0-2.1-.9-.9-.9-.9-2.1V10.8h3v30.15h23.7v3Zm6-6q-1.2 0-2.1-.9-.9-.9-.9-2.1v-28q0-1.2.9-2.1.9-.9 2.1-.9h22q1.2 0 *******.9.9 2.1v28q0 1.2-.9 2.1-.9.9-2.1.9Zm0-3h22v-28H15v28Zm0 0v-28 28Z'/%3E%3C/svg%3E");
     -webkit-mask-size: contain;
     mask-size: contain;
    //  background-color: var(--gray-400);
     display: block;
     height: 24px;
     width: 24px;
 }

//  [data-copy-state="copy"] span:empty:hover::before {
//     background-color: var(--white);
// }

 [data-copy-state="copy-success"] span:empty::before {
    content: "";
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M18.9 35.7 7.7 24.5l2.15-2.15 9.05 9.05 19.2-19.2 2.15 2.15Z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M18.9 35.7 7.7 24.5l2.15-2.15 9.05 9.05 19.2-19.2 2.15 2.15Z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    // background-color: var(--emerald-200);
    display: block;
    height: 24px;
    width: 24px;
 }

 [data-copy-state="copy-error"] span:empty::before {
    content: "";
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M24 34q.7 0 1.175-.475.475-.475.475-1.175 0-.7-.475-1.175Q24.7 30.7 24 30.7q-.7 0-1.175.475-.475.475-.475 1.175 0 .7.475 1.175Q23.3 34 24 34Zm-1.35-7.65h3V13.7h-3ZM24 44q-4.1 0-7.75-1.575-3.65-1.575-6.375-4.3-2.725-2.725-4.3-6.375Q4 28.1 4 23.95q0-4.1 1.575-7.75 1.575-3.65 4.3-6.35 2.725-2.7 6.375-4.275Q19.9 4 24.05 4q4.1 0 7.75 1.575 3.65 1.575 6.35 4.275 2.7 2.7 4.275 6.35Q44 19.85 44 24q0 4.1-1.575 7.75-1.575 3.65-4.275 6.375t-6.35 4.3Q28.15 44 24 44Zm.05-3q7.05 0 12-4.975T41 23.95q0-7.05-4.95-12T24 7q-7.05 0-12.025 4.95Q7 16.9 7 24q0 7.05 4.975 12.025Q16.95 41 24.05 41ZM24 24Z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48' viewBox='0 0 48 48'%3E%3Cpath d='M24 34q.7 0 1.175-.475.475-.475.475-1.175 0-.7-.475-1.175Q24.7 30.7 24 30.7q-.7 0-1.175.475-.475.475-.475 1.175 0 .7.475 1.175Q23.3 34 24 34Zm-1.35-7.65h3V13.7h-3ZM24 44q-4.1 0-7.75-1.575-3.65-1.575-6.375-4.3-2.725-2.725-4.3-6.375Q4 28.1 4 23.95q0-4.1 1.575-7.75 1.575-3.65 4.3-6.35 2.725-2.7 6.375-4.275Q19.9 4 24.05 4q4.1 0 7.75 1.575 3.65 1.575 6.35 4.275 2.7 2.7 4.275 6.35Q44 19.85 44 24q0 4.1-1.575 7.75-1.575 3.65-4.275 6.375t-6.35 4.3Q28.15 44 24 44Zm.05-3q7.05 0 12-4.975T41 23.95q0-7.05-4.95-12T24 7q-7.05 0-12.025 4.95Q7 16.9 7 24q0 7.05 4.975 12.025Q16.95 41 24.05 41ZM24 24Z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    background-color: var(--cardinal-300);
    display: block;
    height: 24px;
    width: 24px;
 }

 div.code-toolbar > .toolbar > .toolbar-item > a,
 div.code-toolbar > .toolbar > .toolbar-item > button,
 div.code-toolbar > .toolbar > .toolbar-item > span {
     color: #bbb;
     font-size: .8em;
     padding: 4px;
     background: rgba(224, 224, 224, 0.2);
     box-shadow: 0 2px 0 0 rgba(0,0,0,0.2);
     border-radius: 4px;
 }

 div.code-toolbar > .toolbar > .toolbar-item > a:hover,
 div.code-toolbar > .toolbar > .toolbar-item > a:focus,
 div.code-toolbar > .toolbar > .toolbar-item > button:hover,
 div.code-toolbar > .toolbar > .toolbar-item > button:focus,
 div.code-toolbar > .toolbar > .toolbar-item > span:hover,
 div.code-toolbar > .toolbar > .toolbar-item > span:focus {
     color: inherit;
     text-decoration: none;
 }

 .token.treeview-part .entry-line {
     position: relative;
     text-indent: -99em;
     display: inline-block;
     vertical-align: top;
     width: 1.2em;
 }
 .token.treeview-part .entry-line:before,
 .token.treeview-part .line-h:after {
     content: "";
     position: absolute;
     top: 0;
     left: 50%;
     width: 50%;
     height: 100%;
 }
 .token.treeview-part .line-h:before,
 .token.treeview-part .line-v:before {
     border-left: 1px solid #ccc;
 }
 .token.treeview-part .line-v-last:before {
     height: 50%;
     border-left: 1px solid #ccc;
     border-bottom: 1px solid #ccc;
 }
 .token.treeview-part .line-h:after {
     height: 50%;
     border-bottom: 1px solid #ccc;
 }
 .token.treeview-part .entry-name {
     position: relative;
     display: inline-block;
     vertical-align: top;
 }
 .token.treeview-part .entry-name.dotfile {
     opacity: 0.5;
 }

 /* @GENERATED-FONT */
 @font-face {
     font-family: "PrismTreeview";
     /**
      * This font is generated from the .svg files in the `icons` folder. See the `treeviewIconFont` function in
      * `gulpfile.js/index.js` for more information.
      *
      * Use the following escape sequences to refer to a specific icon:
      *
      * - \ea01 file
      * - \ea02 folder
      * - \ea03 image
      * - \ea04 audio
      * - \ea05 video
      * - \ea06 text
      * - \ea07 code
      * - \ea08 archive
      * - \ea09 pdf
      * - \ea0a excel
      * - \ea0b powerpoint
      * - \ea0c word
      */
     src: url("data:application/font-woff;base64,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")
         format("woff");
 }

 .token.treeview-part .entry-name:before {
     content: "\ea01";
     font-family: "PrismTreeview";
     font-size: inherit;
     font-style: normal;
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale;
     width: 2.5ex;
     display: inline-block;
 }

 .token.treeview-part .entry-name.dir:before {
     content: "\ea02";
 }
 .token.treeview-part .entry-name.ext-bmp:before,
 .token.treeview-part .entry-name.ext-eps:before,
 .token.treeview-part .entry-name.ext-gif:before,
 .token.treeview-part .entry-name.ext-jpe:before,
 .token.treeview-part .entry-name.ext-jpg:before,
 .token.treeview-part .entry-name.ext-jpeg:before,
 .token.treeview-part .entry-name.ext-png:before,
 .token.treeview-part .entry-name.ext-svg:before,
 .token.treeview-part .entry-name.ext-tiff:before {
     content: "\ea03";
 }
 .token.treeview-part .entry-name.ext-cfg:before,
 .token.treeview-part .entry-name.ext-conf:before,
 .token.treeview-part .entry-name.ext-config:before,
 .token.treeview-part .entry-name.ext-csv:before,
 .token.treeview-part .entry-name.ext-ini:before,
 .token.treeview-part .entry-name.ext-log:before,
 .token.treeview-part .entry-name.ext-md:before,
 .token.treeview-part .entry-name.ext-nfo:before,
 .token.treeview-part .entry-name.ext-txt:before {
     content: "\ea06";
 }
 .token.treeview-part .entry-name.ext-asp:before,
 .token.treeview-part .entry-name.ext-aspx:before,
 .token.treeview-part .entry-name.ext-c:before,
 .token.treeview-part .entry-name.ext-cc:before,
 .token.treeview-part .entry-name.ext-cpp:before,
 .token.treeview-part .entry-name.ext-cs:before,
 .token.treeview-part .entry-name.ext-css:before,
 .token.treeview-part .entry-name.ext-h:before,
 .token.treeview-part .entry-name.ext-hh:before,
 .token.treeview-part .entry-name.ext-htm:before,
 .token.treeview-part .entry-name.ext-html:before,
 .token.treeview-part .entry-name.ext-jav:before,
 .token.treeview-part .entry-name.ext-java:before,
 .token.treeview-part .entry-name.ext-js:before,
 .token.treeview-part .entry-name.ext-php:before,
 .token.treeview-part .entry-name.ext-rb:before,
 .token.treeview-part .entry-name.ext-xml:before {
     content: "\ea07";
 }
 .token.treeview-part .entry-name.ext-7z:before,
 .token.treeview-part .entry-name.ext-bz:before,
 .token.treeview-part .entry-name.ext-bz2:before,
 .token.treeview-part .entry-name.ext-gz:before,
 .token.treeview-part .entry-name.ext-rar:before,
 .token.treeview-part .entry-name.ext-tar:before,
 .token.treeview-part .entry-name.ext-tgz:before,
 .token.treeview-part .entry-name.ext-zip:before {
     content: "\ea08";
 }
 .token.treeview-part .entry-name.ext-aac:before,
 .token.treeview-part .entry-name.ext-au:before,
 .token.treeview-part .entry-name.ext-cda:before,
 .token.treeview-part .entry-name.ext-flac:before,
 .token.treeview-part .entry-name.ext-mp3:before,
 .token.treeview-part .entry-name.ext-oga:before,
 .token.treeview-part .entry-name.ext-ogg:before,
 .token.treeview-part .entry-name.ext-wav:before,
 .token.treeview-part .entry-name.ext-wma:before {
     content: "\ea04";
 }
 .token.treeview-part .entry-name.ext-avi:before,
 .token.treeview-part .entry-name.ext-flv:before,
 .token.treeview-part .entry-name.ext-mkv:before,
 .token.treeview-part .entry-name.ext-mov:before,
 .token.treeview-part .entry-name.ext-mp4:before,
 .token.treeview-part .entry-name.ext-mpeg:before,
 .token.treeview-part .entry-name.ext-mpg:before,
 .token.treeview-part .entry-name.ext-ogv:before,
 .token.treeview-part .entry-name.ext-webm:before {
     content: "\ea05";
 }
 .token.treeview-part .entry-name.ext-pdf:before {
     content: "\ea09";
 }
 .token.treeview-part .entry-name.ext-xls:before,
 .token.treeview-part .entry-name.ext-xlsx:before {
     content: "\ea0a";
 }
 .token.treeview-part .entry-name.ext-doc:before,
 .token.treeview-part .entry-name.ext-docm:before,
 .token.treeview-part .entry-name.ext-docx:before {
     content: "\ea0c";
 }
 .token.treeview-part .entry-name.ext-pps:before,
 .token.treeview-part .entry-name.ext-ppt:before,
 .token.treeview-part .entry-name.ext-pptx:before {
     content: "\ea0b";
 }

