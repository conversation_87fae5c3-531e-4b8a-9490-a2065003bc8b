/* PrismJS 1.29.0
https://prismjs.com/download.html#themes=prism-twilight&languages=markup+css+clike+javascript */
/*
 prism.js Twilight theme
 Based (more or less) on the Twilight theme originally of Textmate fame.
 <AUTHOR>

 Adapted for Lotus Docs by <PERSON>
 Website: https://colinwilson.uk
 Twitter Handle: https://twitter.com/colinwilsonuk
*/

:root {
    --prism-code-color: var(--white);
    --prism-code-bg: #141414;
    --prism-code-scrollbar-thumb-color: var(--gray-400);
    // --prism-line-highlight-bg-color: hsl(215, 15%, 59%); /* #8794A6 */
    --prism-line-highlight-bg-color: var(--gray-600);
    --prism-copy-btn-bg-hover-color: var(--white);
}

[data-dark-mode] {
    --prism-code-color: #dee2e6;
    --prism-code-bg: var(--gray-900);
    --prism-code-scrollbar-thumb-color: var(--gray-600);
    --prism-line-highlight-bg-color: var(--gray-600);
    --prism-copy-btn-bg-hover-color: var(--white);
}

code[class*="language-"],
pre[class*="language-"] {
    color: var(--prism-code-color);
    background: var(--prism-code-bg) !important;
}

pre[class*="language-"],
:not(pre)>code[class*="language-"] {
    background: hsl(0, 0%, 8%);
    /* #141414 */
}

/* Code blocks */
pre[class*="language-"] {
    // padding: 1em;
    // margin: .5em 0;
    // border: .3em solid hsl(0, 0%, 33%); /* #282A2B */
    // box-shadow: 1px 1px .5em black inset;
    overflow: auto;
    border-radius: 0 0 4px 4px;
}

pre[class*="language-"]::-moz-selection {
    /* Firefox */
    background: hsl(200, 4%, 16%);
    /* #282A2B */
}

pre[class*="language-"]::selection {
    /* Safari */
    background: hsl(200, 4%, 16%);
    /* #282A2B */
}

/* Text Selection colour */
pre[class*="language-"]::-moz-selection,
pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection,
code[class*="language-"] ::-moz-selection {
    text-shadow: none;
    background: hsla(0, 0%, 93%, 0.15);
    /* #EDEDED */
}

pre[class*="language-"]::selection,
pre[class*="language-"] ::selection,
code[class*="language-"]::selection,
code[class*="language-"] ::selection {
    text-shadow: none;
    background: hsla(0, 0%, 93%, 0.15);
    /* #EDEDED */
}

/* Inline code */
:not(pre)>code[class*="language-"] {
    //  border: .13em solid hsl(0, 0%, 33%); /* #545454 */
    //  box-shadow: 1px 1px .3em -.1em black inset;
    padding: .1em;
    border-radius: .3em;
    white-space: normal;
}

.line-highlight.line-highlight {
    background: linear-gradient(to right, hsla(0, 0%, 33%, .1) 70%, hsla(0, 0%, 33%, 0));
    /* #545454 */
    border-bottom: 1px dashed hsl(0, 0%, 33%);
    /* #545454 */
    border-top: 1px dashed hsl(0, 0%, 33%);
    /* #545454 */
    z-index: 0;
}

.line-highlight:before,
.line-highlight[data-end]:after {
    background-color: var(--prism-line-highlight-bg-color);
}

[data-copy-state="copy"] span:empty::before {
    background-color: var(--gray-500);
}

[data-copy-state="copy"] span:empty:hover::before {
    background-color: var(--prism-copy-btn-bg-hover-color);
}

[data-copy-state="copy-success"] span:empty::before {
    background-color: var(--emerald-200);
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: hsl(0, 0%, 47%);
    /* #777777 */
}

.token.punctuation {
    opacity: .7;
}

.token.namespace {
    opacity: .7;
}

.token.tag,
.token.boolean,
.token.number,
.token.deleted {
    color: hsl(14, 58%, 55%);
    /* #CF6A4C */
}

.token.keyword,
.token.property,
.token.selector,
.token.constant,
.token.symbol,
.token.builtin {
    color: hsl(53, 89%, 79%);
    /* #F9EE98 */
}

.token.attr-name,
.token.attr-value,
.token.string,
.token.char,
.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable,
.token.inserted {
    color: hsl(76, 21%, 52%);
    /* #8F9D6A */
}

.token.atrule {
    color: hsl(218, 22%, 55%);
    /* #7587A6 */
}

.token.function,
.token.class-name {
    color: var(--prism-code-color);
}

.token.regex,
.token.important {
    color: hsl(42, 75%, 65%);
    /* #E9C062 */
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

/* Markup */
.language-markup .token.tag,
.language-markup .token.attr-name,
.language-markup .token.punctuation {
    color: hsl(33, 33%, 52%);
    /* #AC885B */
}

/* Make the tokens sit above the line highlight so the colours don't look faded. */
.token {
    position: relative;
    z-index: 1;
}