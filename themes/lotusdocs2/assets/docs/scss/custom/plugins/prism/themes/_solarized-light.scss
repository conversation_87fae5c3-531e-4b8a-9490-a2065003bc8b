/* PrismJS 1.29.0
https://prismjs.com/download.html#themes=prism-solarizedlight&languages=markup+css+clike+javascript */
/*
 Solarized Color Schemes originally by <PERSON>
 http://ethanschoonover.com/solarized

 Ported for PrismJS by <PERSON>
 Website: https://krakendev.io
 Twitter Handle: https://twitter.com/allonsykraken

 Adapted for Lotus Docs by <PERSON>
 Website: https://colinwilson.uk
 Twitter Handle: https://twitter.com/colinwilsonuk
*/

/*
SOLARIZED HEX
--------- -------
base03    #002b36
base02    #073642
base01    #586e75
base00    #657b83
base0     #839496
base1     #93a1a1
base2     #eee8d5
base3     #fdf6e3
yellow    #b58900
orange    #cb4b16
red       #dc322f
magenta   #d33682
violet    #6c71c4
blue      #268bd2
cyan      #2aa198
green     #859900
*/

:root {
    --prism-code-color: #657b83;
    --prism-code-bg: #fdf6e3;
    --prism-code-scrollbar-thumb-color: var(--gray-400);
    --prism-line-highlight-bg-color: #e9967a;
    --prism-copy-btn-bg-hover-color: var(--gray-700);
}

[data-dark-mode] {
    --prism-code-color: #dee2e6;
    --prism-code-bg: var(--gray-900);
    --prism-code-scrollbar-thumb-color: var(--gray-600);
    --prism-line-highlight-bg-color: var(--gray-600);
    --prism-copy-btn-bg-hover-color: var(--white);
}

code[class*="language-"],
pre[class*="language-"] {
	color: var(--prism-code-color); /* base00 */
    background: var(--prism-code-bg) !important;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	background: #073642; /* base02 */
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	background: #073642; /* base02 */
}

/* Code blocks */
pre[class*="language-"] {
	// padding: 1em;
	// margin: .5em 0;
	overflow: auto;
	border-radius: 0 0 4px 4px;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background-color: #fdf6e3; /* base3 */
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
    white-space: normal;
}

.line-highlight:before,
.line-highlight[data-end]:after {
    background-color: var(--prism-line-highlight-bg-color);
}

[data-copy-state="copy"] span:empty::before {
    background-color: var(--gray-500);
}

[data-copy-state="copy"] span:empty:hover::before {
    background-color: var(--prism-copy-btn-bg-hover-color);
}

[data-copy-state="copy-success"] span:empty::before {
    background-color: var(--emerald-400);
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: #93a1a1; /* base1 */
}

.token.punctuation {
	color: #586e75; /* base01 */
}

.token.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
	color: #268bd2; /* blue */
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.url,
.token.inserted {
	color: #2aa198; /* cyan */
}

.token.entity {
	color: #657b83; /* base00 */
	background: #eee8d5; /* base2 */
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #859900; /* green */
}

.token.function,
.token.class-name {
	color: #b58900; /* yellow */
}

.token.regex,
.token.important,
.token.variable {
	color: #cb4b16; /* orange */
}

.token.important,
.token.bold {
	font-weight: bold;
}
.token.italic {
	font-style: italic;
}

.token.entity {
	cursor: help;
}