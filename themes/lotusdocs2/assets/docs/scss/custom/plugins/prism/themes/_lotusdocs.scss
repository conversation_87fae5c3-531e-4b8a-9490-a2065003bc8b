 /**
 * Lotus Docs theme
 *
 * Adapted from a theme based on:
 * https://github.com/chriske<PERSON>on/tomorrow-theme
 *
 * <AUTHOR> <github.com/colinwilson>
 * @version 1.0
 */

:root {
    --prism-code-bg: #212d63;
    --prism-code-scrollbar-thumb-color: var(--gray-400);
}

[data-dark-mode] {
    --prism-code-bg: var(--gray-900);
    --prism-code-scrollbar-thumb-color: var(--gray-600);
}

 code[class*="language-"],
 pre[class*="language-"] {
     color: #f5fbff !important;
     background: var(--prism-code-bg) !important;
 }

/* Code blocks */
pre[class*="language-"] {
    // padding: 1em;
    // margin: .5em 0;
    overflow: auto;
    border-radius: 0 0 4px 4px;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
    background: #32325d;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
    padding: .1em;
    border-radius: .3em;
    white-space: normal;
}

.line-highlight:before,
.line-highlight[data-end]:after {
    background-color: var(--blue-400);
}

[data-copy-state="copy"] span:empty::before {
    background-color: var(--gray-400);
}

[data-copy-state="copy"] span:empty:hover::before {
    background-color: var(--white);
}

[data-copy-state="copy-success"] span:empty::before {
    background-color: var(--emerald-200);
}

.token.comment,
.token.block-comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #999;
}

.token.punctuation {
    color: #ccc;
}

.token.tag,
.token.attr-name,
.token.namespace,
.token.deleted {
    color: #7fd3ed;
}

.token.function-name {
    color: #6196cc;
}

.token.boolean,
.token.function {
    color: #fda3f3;
}

.token.number {
    color: var(--cardinal-200);
}

.token.property,
.token.class-name,
.token.constant,
.token.symbol {
    color: #ffffff;
    font-weight: 700;
}

.token.selector,
.token.important,
.token.atrule,
.token.keyword,
.token.builtin {
    color: #a4cdfe;
    font-weight: 700;
}

.token.string,
.token.char,
.token.attr-value,
.token.regex {
    color: #7ec699;
}

.token.variable {
    color: var(--yellow-100);
}

.token.operator,
.token.entity,
.token.url {
    color: #67cdcc;
}

.token.important,
.token.bold {
    font-weight: bold;
}
.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

.token.inserted {
    color: green;
}