/* Template Name: LotusLabs Docs
   Template Author: <PERSON>
   Template E-mail: <EMAIL>
   Created: October 2022
   Version: 1.0.0
   File Description: Main CSS file of the Landing Page template
   Adapted by: Ondrej <PERSON>
   Contact: <EMAIL>
*/

//Custom Font Variables
$font-family-secondary:  "{{ .Site.Params.secondary_font | default "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Ubuntu'" }}";
$font-family-sans-serif: "{{ .Site.Params.sans_serif_font | default "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Ubuntu'" }}";
$font-family-monospace:  "{{ .Site.Params.mono_font | default "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace" }}";

//Fonts
// @import "custom/fonts/fonts";

//Core files
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "variables";
@import "bootstrap/mixins";
@import "bootstrap/bootstrap";

// Plugins
@import "custom/plugins/icons/google-material";

// Structure
@import "custom/structure/general";
@import "custom/structure/topbar";
// @import "custom/structure/content";
// @import "custom/structure/sidebar-layouts";
// @import "custom/structure/doc-nav";
// @import "custom/structure/toc-layouts";
@import "custom/structure/footer";

// // Components
@import "custom/components/buttons";
@import "custom/components/badge";
@import "custom/components/backgrounds";
// @import "custom/components/alerts";
// @import "custom/components/card";

// // Pages
// @import "custom/pages/blog";

@import "custom/pages/features";
@import "custom/pages/helper";
@import "custom/pages/hero";
@import "custom/pages/comments";
@import "custom/pages/people_grid";
@import "custom/pages/class_variants";
// {{ if ($.Scratch.Get "image_compare_enabled") }}@import "custom/pages/image-compare-viewer";{{ end }}

// @import "custom/pages/simplebar";
