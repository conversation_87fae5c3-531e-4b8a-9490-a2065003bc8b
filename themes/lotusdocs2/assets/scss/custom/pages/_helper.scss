//
// _helper.scss
//
.fw-medium {
    font-weight: $fw-medium;
}

.section {
    padding: 100px 0;
    position: relative;

    @media (max-width: 768px) {
        padding: 60px 0;
    }
}

.section-two {
    padding: 60px 0;
    position: relative;
}

.bg-overlay {
    background-color: $overlay;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%
}

.bg-overlay-white {
    background-color: $bg-overlay-white;
}

.bg-gradient-primary {
    background: $bg-gradient-primary;
}

.bg-linear-gradient {
    background: $linear-gradient;
}

.bg-linear-gradient-2 {
    background: $linear-gradient-2;
}

.bg-linear-gradient-3 {
    background: $linear-gradient-3;
}

.bg-gradient-overlay {
    background: $gradient-overlay;
}

.bg-gradient-overlay-2 {
    background: $gradient-overlay-2;
}

.bg-primary-gradient-overlay {
    background: $primary-gradient-overlay;
}

.bg-gradient-white-overlay {
    background: $gradient-white-overlay;
}

//Title
.title-heading {
    line-height: 26px;

    .heading {
        font-size: 65px !important;
        letter-spacing: 1px;

        @media (max-width: 768px) {
            font-size: 35px !important;
        }
    }

    .para-desc {
        font-size: 1.1em;
    }
}

.section-title {

    // position: relative;
    .title {
        letter-spacing: 0.5px;
        font-size: 30px !important;

        @media (max-width: 768px) {
            font-size: 24px !important;
        }
    }
}

.text-shadow-title {
    text-shadow: 2px 0 0 $white,
        -2px 0 0 $white,
        0 4px 0 rgba($white, 0.4),
        0 -2px 0 $white,
        1px 1px $white,
        -1px -1px 0 $white,
        1px -1px 0 $white,
        -1px 1px 0 $white;
}

.para-desc {
    max-width: 800px;
}

.mt-100 {
    margin-top: 100px;
}

//Feature post placeholder
.feature-posts-placeholder {
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    height: 66.6%;

    @media (max-width: 425px) {
        height: 80%;
    }
}

@media (min-width: 768px) {
    .margin-top-100 {
        margin-top: 100px;
    }
}

@media (max-width: 768px) {
    .mt-60 {
        margin-top: 60px;
    }
}

@keyframes animate {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
        border-radius: 10px;
    }

    100% {
        transform: translateY(-1000px) rotate(720deg);
        opacity: 0;
    }
}

// Rounded
.rounded {
    border-radius: 4px !important;
}

.rounded-top {
    border-top-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
}

.rounded-top-1 {
    border-top-left-radius: $border-radius-sm !important;
    border-top-right-radius: $border-radius-sm !important;
}

.rounded-top-2 {
    border-top-left-radius: $border-radius !important;
    border-top-right-radius: $border-radius !important;
}

.rounded-top-3 {
    border-top-left-radius: $border-radius-lg !important;
    border-top-right-radius: $border-radius-lg !important;
}

.rounded-top-4 {
    border-top-left-radius: $border-radius-xl !important;
    border-top-right-radius: $border-radius-xl !important;
}

.rounded-top-5 {
    border-top-left-radius: $border-radius-2xl !important;
    border-top-right-radius: $border-radius-2xl !important;
}

.rounded-left {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
}

.rounded-left-1 {
    border-top-left-radius: $border-radius-sm !important;
    border-bottom-left-radius: $border-radius-sm !important;
}

.rounded-left-2 {
    border-top-left-radius: $border-radius !important;
    border-bottom-left-radius: $border-radius !important;
}

.rounded-left-3 {
    border-top-left-radius: $border-radius-lg !important;
    border-bottom-left-radius: $border-radius-lg !important;
}

.rounded-left-4 {
    border-top-left-radius: $border-radius-xl !important;
    border-bottom-left-radius: $border-radius-xl !important;
}

.rounded-left-5 {
    border-top-left-radius: $border-radius-2xl !important;
    border-bottom-left-radius: $border-radius-2xl !important;
}

.rounded-bottom {
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
}

.rounded-bottom-1 {
    border-bottom-right-radius: $border-radius-sm !important;
    border-bottom-left-radius: $border-radius-sm !important;
}

.rounded-bottom-2 {
    border-bottom-right-radius: $border-radius !important;
    border-bottom-left-radius: $border-radius !important;
}

.rounded-bottom-3 {
    border-bottom-right-radius: $border-radius-lg !important;
    border-bottom-left-radius: $border-radius-lg !important;
}

.rounded-bottom-4 {
    border-bottom-right-radius: $border-radius-xl !important;
    border-bottom-left-radius: $border-radius-xl !important;
}

.rounded-bottom-5 {
    border-bottom-right-radius: $border-radius-2xl !important;
    border-bottom-left-radius: $border-radius-2xl !important;
}

.rounded-right {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
}

.rounded-right-1 {
    border-bottom-right-radius: $border-radius-sm !important;
    border-top-right-radius: $border-radius-sm !important;
}

.rounded-right-2 {
    border-bottom-right-radius: $border-radius !important;
    border-top-right-radius: $border-radius !important;
}

.rounded-right-3 {
    border-bottom-right-radius: $border-radius-lg !important;
    border-top-right-radius: $border-radius-lg !important;
}

.rounded-right-4 {
    border-bottom-right-radius: $border-radius-xl !important;
    border-top-right-radius: $border-radius-xl !important;
}

.rounded-right-5 {
    border-bottom-right-radius: $border-radius-2xl !important;
    border-top-right-radius: $border-radius-2xl !important;
}

.rounded-md {
    border-radius: 10px !important;
}

.rounded-lg {
    border-radius: 30px !important;
}

//Border
@each $name,
$value in $theme-colors {
    .border-#{$name} {
        border-color: #{$value} !important;
    }
}

// Opacity
.opacity-05 {
    opacity: 0.05;
}

.opacity-075 {
    opacity: 0.075;
}

.opacity-1 {
    opacity: 0.1;
}

.opacity-2 {
    opacity: 0.2;
}

.opacity-3 {
    opacity: 0.3;
}

.opacity-4 {
    opacity: 0.4;
}

.opacity-5 {
    opacity: 0.5;
}

.opacity-6 {
    opacity: 0.6;
}

.opacity-7 {
    opacity: 0.7;
}

.opacity-8 {
    opacity: 0.8;
}

.opacity-9 {
    opacity: 0.9;
}

//Z Index css
.z-index-1 {
    z-index: 1;
}

.z-index-0 {
    z-index: 0;
}

.z-index-m-1 {
    z-index: -1;
}

//Small
.small,
small {
    font-size: 90%;
}


//Social icons
.social-icon {
    li {
        a {
            color: $dark;
            border: 1px solid $dark;
            height: 32px;
            width: 32px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            transition: all 0.4s ease;
            overflow: hidden;
            position: relative;

            .fea-social {
                stroke-width: 2;
            }

            &:hover {
                background-color: $primary;
                border-color: $primary  !important;
                color: $foot-social-color-white  !important;

                .fea-social {
                    fill: $primary;
                }
            }
        }
    }

    &.social {
        li {
            a {
                color: $foot-social-color;
                border-color: $social-border-color;
            }
        }
    }

    &.foot-social-icon {
        li {
            a {
                color: $foot-social-color;
                border-color: lighten($footer, 5%);
            }
        }
    }
}

//back to top
.back-to-top {
    position: fixed;
    z-index: 99;
    bottom: 30px;
    right: 30px;
    display: none;
    transition: all 0.5s ease;
    height: 36px;
    width: 36px;
    line-height: 33px;
    border-radius: 6px;
    border: none;
    text-align: center;
    text-align: -moz-center;
    text-align: -webkit-center;
    padding-top: 7px;
    background: $primary;
    color: $back-to-top-color  !important;

    .icons {
        transition: all 0.5s ease;
    }

    &:hover {
        transform: rotate(45deg);

        .icons {
            transform: rotate(-45deg) !important;
        }
    }
}

//position-middle-bottom
.position-middle-bottom {
    position: absolute;
    bottom: 15px;
    left: 12px;
    right: 12px;
    text-align: center;
}

//Text-color-white
.text-color-white {
    color: $text-color-white  !important;
}

.d-flex {
    .flex-1 {
        flex: 1;
    }
}

//Google Material Symbol Icons
.material-icons {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    /* Preferred icon size */
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    word-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    overflow: hidden;

    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;

    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;

    /* Support for IE. */
    font-feature-settings: 'liga';
}

/* Recommended icon sizes */
span,
i {
    &.size-16 {
        font-size: 16px !important;
        font-variation-settings: 'OPSZ' 16;
    }
    &.size-20 {
        font-size: 20px !important;
        font-variation-settings: 'OPSZ' 20;
    }
    &.size-24 {
        font-size: 24px !important;
        font-variation-settings: 'OPSZ' 24;
    }
    &.size-40 {
        font-size: 40px !important;
        font-variation-settings: 'OPSZ' 40;
    }
    &.size-48 {
        font-size: 48px !important;
        font-variation-settings: 'OPSZ' 48;
    }
}

/* NavBar menu icon - add rounded square background */
span.icon-bg {
    background: $primary;
    color: rgba(255, 255, 255, 1) !important;
    font-variation-settings: 'GRAD' -25;
    margin-left: -8px !important;
    padding: 3px;
    border-radius: 3px;
}

/* NavBar menu icon spacing */
span.menu-icon {
    margin: 0px 5px 3px -2px;
}

/* Text justify */
.text-justify {
    text-align: justify;
}

//back to top
.back-to-top {
    position: fixed;
    z-index: 99;
    bottom: 30px;
    right: 30px;
    display: none;
    transition: all 0.5s ease;
    height: 36px;
    width: 36px;
    line-height: 0;
    padding: 0;
    border-radius: 4px;
    border: 1px solid  $primary;
    background: $primary;

    svg {
        transition: all 0.5s ease;
    }

    &:hover {
        transform: rotate(45deg);
        svg {
            transform: rotate(-45deg) !important;
        }
    }
}