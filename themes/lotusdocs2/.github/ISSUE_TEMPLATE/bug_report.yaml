name: "🐛 Bug Report"
description: Create a report to help us improve Lotus Docs
body:
  - type: markdown
    attributes:
     value: |
       Thanks for taking the time to fill out this bug report!

       Please note that this tracker is only for bugs. Do not use the issue tracker for help or feature requests.

       [Our docs](https://lotusdocs.dev/docs) are a great place for most answers, but if you can't find your answer there, you can ask in [community discussion forum](https://github.com/colinwilson/lotusdocs/discussions/categories/q-a).

       Have a feature request? Please search the ideas [on our forum](https://github.com/colinwilson/lotusdocs/discussions/categories/feature-requests) to make sure that the feature has not yet been requested. If you cannot find what you had in mind, please [submit your feature request here](https://github.com/colinwilson/lotusdocs/discussions/new?category=feature-requests).

       Want to show off your Lotus Docs themed website? Post a link, screenshot (optional), and details in [our Show & tell forum](https://github.com/colinwilson/lotusdocs/discussions/new?category=show-and-tell).

       **Thanks!**
  - type: checkboxes
    attributes:
      label: Past Issues Searched
      options:
        - label: >-
            I have searched open and closed issues to make sure that the bug has
            not yet been reported
          required: true
  - type: checkboxes
    attributes:
      label: Issue is a Bug Report
      options:
        - label: >-
            This is a bug report and not a feature request, nor asking for support
          required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is
      placeholder: Tell us what happened!
    validations:
      required: true
  - type: textarea
    id: bug-expectation
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen
      placeholder: Tell us what you expected
    validations:
      required: true
  - type: textarea
    id: bug-screenshots
    attributes:
      label: Screenshots
      description: 'If applicable, add screenshots to help explain your problem'
      placeholder: Insert screenshots here
  - type: textarea
    attributes:
      label: Environment
      description: |
        examples:
        - **OS**: MacOS
        - **Browser**: Firefox
        - **Browser Version**: 115
      value: |
        - OS:
        - Browser:
        - Browser Version:
      render: markdown
