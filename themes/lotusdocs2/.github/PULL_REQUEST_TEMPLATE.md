### Changes

Please describe the changes made in the pull request here.

Below you'll find a checklist. For each item on the list, check one option and delete the other.

<!-- ### Tests
- [ ] Automated tests have been added
- [ ] This PR does not require tests -->

<!-- ### Changelog
- [ ] Entry has been added to changelog
- [ ] This PR does not make a user-facing change -->

<!-- ### Documentation
- [ ] [Docs](https://github.com/colinwilson/lotusdocs.dev) have been updated
- [ ] This change does not need a documentation update -->

### Dark mode
- [ ] The UI has been tested both in dark and light mode
- [ ] This PR does not change the UI
