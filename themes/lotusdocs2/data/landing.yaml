# Note: Template blocks require a 'weight' parameter so they're correctly ordered on the landing page

# Hero
hero:
  enable: true
  weight: 10
  template: hero

  backgroundImage:
    path: "images/templates/hero"
    filename:
      desktop: "gradient-desktop.webp"
      mobile: "gradient-mobile.webp"

  # badge:
  #   text: v0.1.0
  #   color: primary # primary, secondary, success, danger, warning, info, light, dark
  #   pill: false # boolean
  #   soft: true # boolean

  # titleLogo:
  #   path: "images/logos"
  #   filename: "title_logo.png"
  #   alt: "Lotus Docs Logo"
  #   height: 80px

  title: "Introduction to Security"
  subtitle: "The Introduction to Security course is a **14-week, free**, intense **hands-on** course to foster knowledge about penetration testing and advanced cyber defenses. "
  image:
    path: "images" # path to image under configured assets directory. default 'images'
    filename: "illustration_class_rotated.png" # filename of your hero image (including file extension)
    alt: "Class ilustration" # Optional but recommended
    boxShadow: true # default 'false' (excludes .svg images)
    rounded: true # round the image corners? default 'false' (excludes .svg images)

  info: From basic security principles to advanced attacks, you will learn to attack in an isolated cyber range and learn how to detect and stop advanced intruders. The online version is free and gives you access to live classes, recordings, and a cyber range to practice what is taught during the lessons.
  info2: This course is part of the [Open Informatics Master](https://oi.fel.cvut.cz/en/master-program), taught at the [Czech Technical University in Prague](https://fel.cvut.cz/en). Classes are taught by members of the [Stratosphere Laboratory](https://www.stratosphereips.org/) in the [AI Center](https://www.aic.fel.cvut.cz/), [Faculty of Electrical Engineering](https://fel.cvut.cz/en), [Czech Technical University in Prague](https://fel.cvut.cz/en).
  ctaButton:
    icon: rocket_launch
    btnText: "Register now"
    url: "#course-variants"
  cta2Button:
    icon: book
    btnText: "Learn More"
    url: "docs/about/"
  
  statistics:
    year: 9
    yearTextSuffix: "successful year of the course"
    students: 2000
    studentsTextSuffix: "enrolled students since 2017"



# Feature Grid
featureGrid:
  enable: true
  weight: 20
  template: feature grid

  title: What does the course offer?
  subtitle:
    This hands-on course will walk students through all the stages of a classical penetration testing assessment. Through alternating attack and defense classes, students will learn to attack and also defend real systems. Online students will be able to try, test, and reproduce what is taught in class thanks to our free software cyber range.
    This one-semester (14-week) course is free, online, and open to anyone in the world. 

  items:
    - title: Intense & Hands-on
      icon: speed
      description: The course is fast-paced and very practical. Each concept we teach needs to be tried to be completely understood. Every topic shown in the course is practiced and done by you to deepen your knowledge and give you experience.
 
    - title: From Zero to Hero
      icon: trending_up
      description: The course is open to anyone who is tech-savvy and familiar with command line knowledge. Previous security experience is not a requirement. You will learn networking, security fundamentals, pentesting, and defensive strategies. In one semester, you will exploit vulnerabilities, attack web pages, and use machine learning for defense.

    - title: Certificate of Completion
      icon: workspace_premium
      description: Upon completing the course, students will receive a certificate of completion the Czech Technical University in Prague (Pro Tier). This is an official certificate valid across the EU. Successful completion of the Free Tier version is acknowledged by Stratosphere Laboratory certificate.
    
    - title: Red Teaming
      icon: destruction
      description: Attack-focused classes will give you real-world experience on how attacks work, how to make them happen, and how hard and fun is to exploit a remote computer. This is essential knowledge to learn to defend better.

    - title: Blue Teaming
      icon: security
      description: "Defense-focused classes will give you real-world experience on one of the most difficult jobs in cybersecurity: how to defend. From system hardening and network forensics to using Machine Learning detection models to detect malware."
 
    - title: International Team
      icon: translate
      description: Our teaching team is composed of individuals of diverse backgrounds, ethnicities, and experiences. We all share a common passion for teaching and are dedicated to ensuring all our students have good learning experiences.

# class variants
classVariants:
  enable: true
  weight: 21
  template: class variants

  title: Course variants
  subtitle: This course is originates from a **B4M36BSY Introduction to Security** course of [Czech Technical University in Prague](https://fel.cvut.cz/en). To participate in the course students have to be either enrolled in the university study programme or register for the online version of the course.

  items:
      - title: Free Tier
        description: "Full access to all course lectures and the local cyber range (run on the student’s own computer). This tier is open to anyone who registers. Participants get access to live tutorial streams, support materials, and the course communication platform. The free version does not include weekly assignments or a final exam. Upon completion, students receive a **Certificate of Completion** from the Stratosphere Laboratory."
        access_fee: "Free"
        learn_more_url: /docs/class-variants/free-tier/
        register_url: https://www.eventbrite.com/e/introduction-to-security-2025-tickets-1387253796449
        regBtnIcon: public
        base_features:
          - "Access to all course lectures"
          - "Access to the local cyber range"
          - "Access to live tutorial streams"
          - "Access to support materials"
          - "Access to the course communication platform"
      
      - title: Professional Tier
        description: "Full access to all course lectures and the cloud-based cyber range. Available to anyone who registers through Eventbrite. Students get access to live tutorial streams, support materials, the course communication platform, and participate in weekly hands-on tutorials and a final exam. This tier is designed for those seeking deeper learning experience and a recognized credential. Graduates receive a **EU Recognized Official Certificate** issued by the Czech Technical University in Prague."
        access_fee: "Paid"
        learn_more_url: /docs/class-variants/professional-tier/
        register_url: https://czv.cvut.cz/en/ee3d4173-b241-486a-ad58-61d7339e5dbf-introduction-to-computer-security/
        regBtnIcon: workspace_premium
        base_features:
          - "Access to all course lectures"
          - "Access to the local cyber range"
          - "Access to live tutorial streams"
          - "Access to support materials"
          - "Access to the course communication platform"
        extra:
          - "Cloud-based cyber range"
          - "Weekly hands-on assignments"
          - "Final exam"
          - "Certificate issued by the Czech Technical University in Prague"
      
      - title: CTU Students
        description: "Full access to all course lectures and the cloud-based cyber range. **Available to students** enrolled in undergraduate or graduate programs at the Czech Technical University in Prague. Includes weekly hands-on assignments, tutorials, and a final exam as part of an accredited university course. Upon successful completion, students get **6 ECTS credits**."
        access_fee: "Included for students of CTU in Prague"
        learn_more_url: /docs/class-variants/ctu-students/
        register_url:  https://www.kos.cvut.cz/
        regBtnIcon: school
        base_features:
          - "Access to all course lectures"
          - "Access to the local cyber range"
          - "Access to live tutorial streams"
          - "Access to support materials"
          - "Access to the course communication platform"
        extra:
          - "Cloud-based cyber range"
          - "Weekly hands-on assignments"
          - "Final exam"
          - "Winter break bonus assignment"
          - "6 ECTS credits"

peopleGrid:
  enable: true
  weight: 25
  template: people grid

  title: Teaching team
  subtitle: The course has a diverse teaching team with extensive experience in various fields of security. You will be guided by both researchers and practitioners in the field.

  people:
    - title: Sebastian Garcia
      description: Sebastian is an Assistant Professor and security researcher specializing in applied machine learning for network traffic and malware detection. He founded the Stratosphere Laboratory at the Czech Technical University in Prague. Committed to digital rights, he advocates for free software and ML tools. Co-founder of MatesLab hackspace in Argentina and the Independent Fund for Women in Tech.
      contacts:
        - webpage: https://www.stratosphereips.org 
        - github: eldraco
        - scholar: WsnP6xUAAAAJ
        - linkedin: sebagarcia
      image:
        path: "images/people"
        filename: "sebastian_garcia.jpg"
    
    - title: Maria Rigaki
      description: Maria is a PhD student from Greece. She is focused on adversarial applications of machine learning in cybersecurity. She spent many years in the industry as a software developer and systems architect. Maria has plenty of experience designing and integrating complex systems in telecommunication and emergency response. In her spare time, she enjoys hacking, playing music, and playing chess.
      contacts:
        - webpage: https://mariarigaki.github.io/
        - github: mariarigaki
        - scholar: 90yGfrsAAAAJ
        - linkedin: maria-rigaki
      image:
        path: "images/people"
        filename: "maria_rigaki.jpg"
        
    - title: Ondřej Lukáš
      description: Ondra is a PhD student from Czechia. He is focused on explainable AI applications in cybersecurity. He is a member of the Stratosphere Laboratory at the Czech Technical University in Prague. He is a lecturer in the AI Dětem, an initiative to educate and guide teachers on how to educate in artificial intelligence. He likes to play sports, travel, and cook. Passionate football fan.
      contacts:
        - webpage: https://www.researchgate.net/profile/Ondrej-Lukas-4
        - github: ondrej-lukas
        - scholar: uycJe_QAAAAJ
        - twitter: ondrej.lukas
      image:
        path: "images/people"
        filename: "ondrej_lukas.jpg"

    - title: Veronica Valeros
      description: Veronica is a researcher born in Patagonia, Argentina. She researches on cybercrime, honeypots, intelligence analysis, and helping teams to be more efficient. She works at the Stratosphere Laboratory while pursuing a second master's in Intelligence and Security Studies from Liverpool John Moores University. Co-founder of MatesLab hackspace in Argentina and the Independent Fund for Women in Tech.
      contacts:
        - webpage: https://verovaleros.github.io
        - github: verovaleros
        - scholar: V9VKkWAAAAAJ
        - linkedin: veronicavalerossaracho
      image:
        path: "images/people"
        filename: "veronica_valeros.jpg"

    - title: Lukáš Forst
      description: Lukas is a solutions architect and cyber security enthusiast with extensive engineering experience at companies like Blindspot, Wire, CDN77, and Better Stack. In 2020, he co-founded Mild Blue, developing AI-powered software for hospitals. In 2022, he joined the Stratosphere teaching staff. In 2024, he and Martin co-founded Recon Wave with the mission to enhance how companies manage their attack surface.
      contacts:
        - webpage: https://reconwave.com/
        - github: LukasForst
        - linkedin: lukas-forst
      image:
        path: "images/people"
        filename: "lukas_forst.jpeg"

    - title: Martin Řepa
      description: Martin is an ethical hacker and an alumni of the Introduction to Security course. He has extensive experience as both a software and security engineer. Martin secured third place in a National Cybersecurity competition and identified several critical vulnerabilities in public bug bounty programs. In 2024, he and Lukas co-founded Recon Wave. In his free time, Martin likes to do bouldering and play chess.
      contacts:
        - webpage: https://reconwave.com/
        - github: HappyStoic 
        - linkedin: martinrepa 
      image:
        path: "images/people"
        filename: "martin_repa.jpeg"

    - title: Muris Sladic
      description: Muris is a PhD student from Bosnia and Herzegovina. He is a member of the Stratosphere Laboratory and an alumni of the Introduction to Security class. His research is focused mostly on cyber deception and defense and the impact of generative AI on cybersecurity. He is the author of shelLM, an LLM-based, high-interaction honeypot. He likes playing and listening to music, reading, writing, and playing puzzle games.
      contacts:
        - webpage: https://www.stratosphereips.org
        - github: msladic1
        - scholar: 0m2q32IAAAAJ
        - linkedin: muris-sladi%C4%87-0a3ab4145
      image:
        path: "images/people"
        filename: "muris_sladic.jpg"

# comments
comments:
  enable: true
  weight: 26
  template: comments
  title: Student reviews
  subtitle: Selection of feedback from students who participated in the previous iterations of Introduction to Security course. The feedback is derived from the anonymous evaluation of the course collected by the Czech Technical University in Prague.

  reviews:
    - review: "Probably the best class I've had throughout my studies. I liked the concept of lab and lecture connected together. The homework challenges were fun to do and I feel like I've learned a lot doing them, yet they did not take too much time to solve which is in my opinion exactly how homework should look like. Thanks for the class!"
    - review: "Initially, I was quite skeptical about the idea of one long lecture that would serve also as a lab, but it was a lot better than I expected. BSY covers a wide range of topics, but it doesn't feel overwhelming (as it appeared to me at least).\nThe assignments were a pinnacle of this subject, loved it. They are a form of Capture the Flag by attacking or defending docker containers. The BSY team have clearly put a lot of work into it and, if there was an error in the existing assignments, promptly fixed it or posted a hint. Speaking of the BSY team, I haven't yet seen such an enthusiastic and cooperative group of lecturers.\nI also really liked that each of them had at least one lecture"
    - review: "Easily the best course I have taken at FEE. Extremely interesting topics, with good balance of theoretical and practical parts. The assignments are fun to do and are well made(each has a pop-culture reference theme, which I really liked). That does not mean that the assignments are easy, but the fact that they are engaging helps a lot."
    - review: "This was undoubtedly **the most well-prepared course** I've taken so far, including through my bachelor's degree.\nThe classes were highly practical, with almost every concept being not only taught but also demonstrated. Ensuring that everyone had the opportunity to replicate the learning experience was a priority.\nThe examples of malware and vulnerabilities presented were current, and I strongly believe that some of the vulnerabilities showcased still exist on numerous production servers that remain unpatched today.\nThe class materials were meticulously crafted. Each session came with a comprehensive document that provided a step-by-step guide, along with well-explained example commands.\nThe assignments were both challenging and engaging. Each task was cleverly framed with a pop culture reference, preventing any monotony or dryness. I believe the assignment format was wisely chosen, resembling common CTF scenarios, a prevalent method for cybersecurity professionals to hone their skills.\nThe teachers and TAs were virtually available 24/7, always ready to offer assistance.\nIn summary, it was a superb experience."
    - review: "One of the best courses I have taken in 7 semesters at FEE. It is quite demanding, especially in terms of time (especially if you do the bonus part), but I can highly recommend it. The lectures and assignments were great!"
    - review: "Very unique take on the course, both lectures and exams. Very well prepared homework with sometime unclear assignments, but this was compensated by quick communication with the teachers, who were always willing to explain uncertainties."
    - review: The content is great, the materials are very good, especially the labs, the teachers are enthusiastic and helpful.
    - review: "A wonderful and most importantly very hands-on look at the field of computer security. I very much appreciate the infrastructure and the opportunity provided to try out virtually every aspect discussed in the lectures."
    - review: "Even if this course is not required for you, I highly recommend it. I highly appreaciate the interactivity in the lectures and excercises (though there's not difference between these two int this course). Each week there is a new task, while it was sometimes time-consuming, I found them truly engaging, and I never felt that I was missing some tool or knowledge to be able to complete the task - and if I did, I could ask, because we could have instant communications with all of the teachers."
    - review: "I am always very critical when taking a course, and this is the first time I can't find any flaws or critical errors. From the instructors to the infrastructure they rely on, everything is absolutely incredible."
# sponsors:
sponsorGrid:
  enable: true
  weight: 30
  template: sponsor grid
  title:  Organizations
  subtitle: This course is taught by the Stratosphere Laboratory, AI Center, Department of Computer Science, Faculty of Electrical Engineering, Czech Technical University in Prague. The course is part of the Open Informatics Master program.

  sponsors:
    - title: Czech Technical University in Prague
      url: https://www.cvut.cz/en
      image:
        path: "images/sponsors"
        filename: "logo_cvut_en_doplnkova_verze.svg"
    
    - title: Faculty of Electrical Engineering
      url: https://fel.cvut.cz/en
      image:
        path: "images/sponsors"
        filename: "fee.svg"

    - title: AI Center
      url: https://www.aic.fel.cvut.cz/
      image:
        path: "images/sponsors"
        filename: "aic.svg"
    
    - title: Open Informatics
      url: https://oi.fel.cvut.cz/en/
      image:
        path: "images/sponsors"
        filename: "oi.png"

    - title: Stratosphere Laboratory
      url: https://www.stratosphereips.org/
      image:
        path: "images/sponsors"
        filename: "stratosphere.svg"
