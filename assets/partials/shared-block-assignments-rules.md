**You can NOT**
1. Attack others on the Internet from the docker we are giving you.
2. Attack the assignment servers or CTFd servers
3. Attack other servers and services in the university network (outside of the IP range given to you)
4. Share your code or solution with other students

**You CAN**
1. Attack the given docker **from** the Internet.
2. Attack **from** the local docker network the dockers for other students (inside the local network)