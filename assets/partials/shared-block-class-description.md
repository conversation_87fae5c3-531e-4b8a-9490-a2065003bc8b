This course teaches the fundamentals of cybersecurity through practical, hands-on experience in both attacking and defending. Students will perform penetration tests and learn to counter real attacks, alternating between offensive and defensive classes. Topics covered include reconnaissance, scanning, exploitation, privilege escalation, lateral movement, exfiltration, malware, network security forensics, binary reversing, log analysis, intrusion detection systems, honeypots, and basics of machine learning for security. By the end of the semester, students will be prepared for junior penetration tester roles or to continue as cybersecurity researchers and practitioners.

This course consists of weekly **3-hour blocks** which combine both theory and practical exercises. Students can attend the tutorials in person in room KN:E-107 or online via the [live stream](https://www.youtube.com/playlist?list=PLQL6z4JeTTQmu09ItEQaqjt6tk0KnRsLh) and Matrix communication platform.