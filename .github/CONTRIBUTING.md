# Contributing

All contributions are welcomed, thank you for taking the time to contribute to this project! 

## How can you contribute?

* Report bugs
* Suggest features and ideas
* Pull requests with a solved GitHub issue and new feature
* Pull request with a new content.

## Persistent Git Branches

The following git branches permanent in the repository:

- `main`: contains the stable version of the repository. All new features should be based on this branch.
   
## Naming Git branches for Pull Requests

To keep the Git history clean and facilitate the revision of contributions we 
ask all branches to follow concise namings. These are the branch-naming patterns
to follow when contributing:

- name-bugfix-<>:        pull request branch, contains one bugfix,
- name-docs-<>:          pull request branch, contains documentation work,
- name-enhance-<>:       pull request branch, contains one enhancement (not a new feature, but improvement nonetheless)
- name-feature-<>:       pull request branch, contains a new feature,
- name-refactor-<>:      pull request branch, contains code refactoring,

## What branch should you base your contribution?

As a general rule, base your contribution on the `main` branch.

## Creating a pull request

Commits:
- Commits should do one thing. Keep it simple.
- Commit messages should be easily readable, imperative style ("Fix memory leak in...", not "FixES mem...")

Pull Requests:
- If you have developed multiple features and/or bugfixes, create separate
    branches for each one of them, and request merges for each branch;
- The cleaner you code/change/changeset is, the faster it will be merged.
