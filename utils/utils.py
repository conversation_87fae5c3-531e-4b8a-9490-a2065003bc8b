import argparse
import os
import csv

def create_content(name:str, cert_id:str, cert_path:str="/certificates")->str:
    """
    Creates the Markdown file with the content for the certificate page.
    """
    content = f'''---
weight: 1
title: "Certificate of Completion"
description: "{name} | Certificate ID: {cert_id}"
icon: "verified"
date: "2025-01-29T15:15:45+01:00"
lastmod: "2025-01-29T15:15:45+01:00"
draft: false
toc: false
hidden: true
doc_nav: false
---
{{{{< figure src="{os.path.join("/",cert_path, cert_id)}.png" alt="Certificate of Completion" max-width="60%" >}}}}
<a href='{os.path.join("/",cert_path, cert_id)}.pdf' target='_blank'>Download certificate (PDF)</a>'''
    return content

def create_page(name:str, cert_id:str, page_path:str, cert_path:str="certificates", cert_location:str="static")->None:
    """
    Verifies all components are correctly located for the certificate page creation and creates the page.
    """
    print(f"Creating page for user {name} ({cert_id})")
    pdf_path = os.path.join(cert_location,cert_path,f"{cert_id}.pdf")
    png_path = os.path.join(cert_location,cert_path,f"{cert_id}.png")
    print(pdf_path, png_path)
    if os.path.exists(pdf_path):
        print(f"\tPDF file:'{pdf_path}'")
    else:
        raise FileNotFoundError(f"File `{pdf_path}` does not exist!")
    if os.path.exists(png_path):
        print(f"\tPDF file:'{png_path}'")
    else:
        raise FileNotFoundError(f"File `{png_path}` does not exist!")
    content = create_content(name, cert_id, cert_path)
    with open(f"{os.path.join(page_path, cert_id)}.md", "w") as outfile:
        outfile.write(content)
    print(f"\tPage created in '{os.path.join(page_path, cert_id)}.md'")

def add_pages(userfile:str, page_path:str, cert_path:str, column_names=["Student Name", "Certificate UUID"]):
    with open(userfile, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)  # Read as dictionary
        parsed_data = [{col: row[col] for col in column_names} for row in reader]
    
    for row in parsed_data:
        create_page(row["Student Name"], row["Certificate UUID"], page_path, cert_path, "static")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Tool to add BSY class certificates to the page Author: <NAME_EMAIL>",
        usage="%(prog)s [options]",
    )
    parser.add_argument(
        "--students",
        help="CSV file with students and certificate IDs",
        action="store",
        required=True,
        type=str,
    )
    parser.add_argument(
        "--page_path",
        help="Path to the location of individual cert pages.",
        action="store",
        required=False,
        default="content/certificates",
        type=str,
    )
    parser.add_argument(
        "--certs_path",
        help="Path to the location of the CSV & PDF files with certs. Files MUST be named <cert-id>.{pdf,png}!",
        action="store",
        required=False,
        type=str,
        default="/certificates/"
    )

    args = parser.parse_args()
    add_pages(args.students, args.page_path, args.certs_path)
