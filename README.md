# Introduction to Security Class Website

The Introduction to Security class website is based on <PERSON>, and it's currently built using two components:
- Website Landing Page hosted at https://github.com/stratosphereips/lotusdocs-bsy-theme
- Website Content hosted at https://github.com/stratosphereips/ctu-intro-to-security

Note: _Updating the landing page requires triggering the GitHub Pages CI in the `ctu-intro-to-security` repository. This can be done manually or by pushing to the `main` branch._

## Website Landing Page
The landing page uses a customized Hugo theme hosted in a separate [lotusdocs-bsy-theme](https://github.com/stratosphereips/lotusdocs-bsy-theme) repository. The theme is linked to this repository as a submodule at [ctu-intro-to-security/tree/main/themes](https://github.com/stratosphereips/ctu-intro-to-security/tree/main/themes).

Changes to the landing page are **not automatically** triggering a website rebuilding process. To update the website there are two options:
- Pull, commit and push the latest changes of the `lotusdocs-bsy-theme` submodule to `ctu-intro-to-security`. A push to the `main` branch will trigger the CI actions to deploy a new website
- Manually trigger the CI GitHub Pages Workflow in the [Actions Tab](https://github.com/stratosphereips/ctu-intro-to-security/actions/workflows/static.yml) of the `ctu-intro-to-security` repository, which will pull the latest submodule changes and re-build using that information.

### Landing Page's Content
The landing page's content is defined at [`lotusdocs-bsy-theme/data/landing.yaml`](https://github.com/stratosphereips/lotusdocs-bsy-theme/blob/release/data/landing.yaml), where each building block has a separate section of the YAML file.

### Landing Page's Media
Media files are stored in the [`assets/images/`](https://github.com/stratosphereips/lotusdocs-bsy-theme/tree/release/assets/images) directory. E.g., images stored in `assets/images/picture.png` will be linked by using `images/picture.png.`

### Landing Pages' Components
Each building block of the index page is defined separately in a single file located in [`themes/lotusdocs-bsy-theme/layouts/partials/landing/`](https://github.com/stratosphereips/lotusdocs-bsy-theme/tree/release/layouts/partials/landing) where it is defined how is the data from the landing page configuration file used during the build and converted into HMTL.

## Styling
The style of the page is defined in [lotusdocs-bsy-theme](https://github.com/stratosphereips/lotusdocs-bsy-theme). Modifications should be done by editing the [CSS files](https://github.com/stratosphereips/lotusdocs-bsy-theme/tree/release/assets/scss)
## Website Content

The website is based on Hugo, the content is managed in the repository [ctu-intro-to-security](https://github.com/stratosphereips/ctu-intro-to-security), and with the following structure:
- Content Pages are located at [content/docs](https://github.com/stratosphereips/ctu-intro-to-security/tree/main/content/docs)
- Website configuration, domain redirection, footer, social media links, the title, and page top menu are managed through the [hugo.toml](https://github.com/stratosphereips/ctu-intro-to-security/blob/main/hugo.toml).

### Shared blocks across multiple pages
Shared MD blocks are intended for cases, where the same information needs to be displayed across multiple pages. 
1. Create the md file with the shared content in `assets/partials/<your-file>.md`
2. Link it from each page where you want to display it with `{{< include-md file="partials/<your-file>.md" >}}`

Do not include headers in the shared blocks as they are *NOT* included in the table of content of the page where the block is inserted.

### Adding Certificates of Completion to the page
1. Generate the Certificates (PDF & PNG files). Filenames should be as follows `<cert-ID>.{pdf,png}`.
2. Copy the files in `static/certificates/`.
3. Create a csv file which contains at least following columns: [`Student Name`,`Certificate UUID`]. *INCLUDE HEADERS in the 1st line of the CSV file
4. Run the page generation script as follows:

```
python3 ./utils/utils.py --students=<students_file.csv> --page_path=content/certificates/ --certs_path=certificates/
```
5. Run `hugo server` and check if the certificates are visible (`<baseURL>/certificates/<CERTIFICATE UUID>`)
6. commit and push to git main

### Adding images to pages (OUTSIDE OF THE LANDING PAGE)
Embedding images, videos, and other content directly into the markdown pages is possible. Upload the files in the `static` folder (`static/images`). In the markdown, use the following syntax to link the content: ```{{< figure src="/images/FILENAME " width="XXX" alt="XXX" >}}```. Hugo is processing the markdown during the site-building process. Therefore, absolute paths in the image `src` can't be used and will result in errors.
## Building

### Local Page Building and Preview

Building the page locally is primarily used to develop new features or content. 

The following packages need to be installed:
- Hugo *Extended* (minimum version: 0.120.0)
  - MacOS: `brew install hugo`
- Go (minimum version v1.20)
  - MacOS: `brew install go`
- git

To build the site, run the following command in the root directory:

```bash
:~$ git clone --recurse-submodules https://github.com/stratosphereips/ctu-intro-to-security.git
:~$ cd ctu-intro-to-security/
:~$ hugo server --navigateToChanged --buildDrafts --cleanDestinationDir
```

### Building on GitHub

The website is built and deployed as GitHub Pages using GitHub CI. The GitHub CI file used is at [.github/workflows/static.yml](https://github.com/stratosphereips/ctu-intro-to-security/blob/main/.github/workflows/static.yml).
- The CI is automatically triggered with every push to the `main` branch to the repository `ctu-intro-to-security`
- The CI can be manually triggered by clicking on the `Run Workflow` in the repository [Actions Tab](https://github.com/stratosphereips/ctu-intro-to-security/actions/workflows/static.yml).

# About

This code was developed at the Stratosphere Laboratory at the Czech Technical University in Prague.
