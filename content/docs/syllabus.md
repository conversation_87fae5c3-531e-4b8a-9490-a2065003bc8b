---
weight: 2
title: "Course Syllabus"
description: "Explore the course topics in detail"
icon: "explore"
date: "2024-08-01T11:48:02+02:00"
lastmod: "2024-08-01T11:48:02+02:00"
draft: false
toc: true
---
Classes are held weekly on Thursdays from 14:30 to 17:45 CEST. The topics covered are as follows:

|    Date   | Topic         | Class Goal    |
| --------- | ------------- | ------------- |
| 25.09.2025| Introduction to the Class, Security, and Networking| To introduce the course and organizational information. To start with the basic concepts of security, and network protocols. |
| 02.10.2025| Finding Computers, Scanning and Basic Network Analysis | To learn how to find computers and services during the reconnaissance phase and how to detect this in the network.|
| 09.10.2025| Getting Access. From People to Vulnerabilities| To know the basic ways of attacking another computer and to access them.|
| 16.10.2025| Detecting Intruders in Your Server| To learn to find an intruder inside a computer and how to harden it to avoid future intrusions.|
| 23.10.2025| A Game of Deception| To learn basic deception engineering, installation of honeypots, and use of honeytokens|
| 30.10.2025| Privilege Escalation, Persistence, Side-Channel Attacks| To learn how to increase privileges while attacking and maintaining control over time.|
| 06.11.2025| Virtualization and Threat Intelligence| To learn what virtualization is, its benefits, and its applications. To learn about Threat Intelligence, its uses, and effectiveness in cyber defense.|
| 13.11.2025| Binary Exploitation and Fuzzing | To understand and exploit unintended behavior in binary files. |
| 20.11.2025| Reverse Engineering| Learn about different aspects of reverse engineering and how we can use it in security.|
| 27.11.2025| Automating Attacks with Malware| To understand why automation is needed and how we automate. Also, to understand the need for steganography.|
| 04.12.2025| Manual and Automatic Detection of C&C Channels| To learn how to detect command-and-control channels in the network manually.|
| 11.12.2024| Web Attacks| To understand the risks and vulnerabilities of the Web, to know the basic attack methods and how to exploit some of them.|
| 18.12.2024| Advanced Web Attacks| To perform advanced web attacks in SQLi, XSS, HTTP/2/3, WebSockets attacks, APIs, SSTI, and others.|
| 08.01.2025| AI for Log Analysis, Recap, and The NOC Talk| To see how AI affects cybersecurity, wrap up the semester and share a sneak peek of the behind-the-scenes of the network operation center of the course|
