---
weight: 4
title: "Professional Tier Online Class"
description: "Students registered in the professional tier online course"
icon: "business_center"
date: "2025-06-03T15:26:53+02:00"
lastmod: "2025-06-03T15:26:53+02:00"
draft: false
toc: true
---
## Class modality
<!-- link class description from shared assets-->
{{< include-md file="partials/shared-block-class-description.md" >}}


## Communication channels
<!-- link communication channels from shared assets-->
{{< include-md file="partials/shared-block-communication-channels.md" >}}


## Registration
Online enrollment for the paid version class can be done through [Lifelong Learning Portal](https://czv.cvut.cz/en/ee3d4173-b241-486a-ad58-61d7339e5dbf-introduction-to-computer-security/) of Czech Technical University in Prague. The full price for the course is ~2000 EUR (~50000 CZK). 

## What are participants provided with?
- Detailed coursebook per class with commands and tools used.
- Access to a chat platform to interact and ask questions.
- [Live video stream](https://www.youtube.com/playlist?list=PLQL6z4JeTTQmu09ItEQaqjt6tk0KnRsLh) and [recordings](https://www.youtube.com/playlist?list=PLQL6z4JeTTQk_z3vwSIvn6wIHMeNQFU3d) of each lecture.
-Cloud-based cyber range environment where to practice what is taught during the classes. We take care of the deployment and infrastructure for you.
- Hands-on **practical** assignments to further learn about the concepts and techniques coverd in the classes.
- Full-scale exam at the end of the course.
- Official Certificate of Completion issued by the [Czech Technical University in Prague](https://www.cvut.cz/en) (The [micro-credential](https://education.ec.europa.eu/education-levels/higher-education/micro-credentials) is valid and accepted across the European Union).

## What do participants need for the classes?
- Computer to take the class and participate in hands-on exercise
- Good internet connection to access the class material and labs
- SSH Client to access the cloud cyber range

## Passing requirements
- You have to be registered in [Lifelong Learning Portal](https://czv.cvut.cz/en/ee3d4173-b241-486a-ad58-61d7339e5dbf-introduction-to-computer-security/) of Czech Technical University in Prague and paid the tuition fee to participate in the course.
- Pass the Final Exam at the end of the Course

## Cyber Range
As part of the paid version of the course, participants are provided with access to the cloud cyberrange. No installation or maintenance - we take care about it. It is a safe environment to environment where you can *safely* practice both attacking and defensive techniques shown in the course

### Local Cyber range
If you want to run the cyberrange yourself, use our [Cyber Range](https://github.com/stratosphereips/stratocyberlab) locally in your computer. It is a local, docker-based environment where you can *safely* practice both attacking and defensive techniques shown in the course. 

## Assignments
Several practical assignments are given during the course. Each assignment corresponds to a class and is designed to practice the content covered in the class. Each assignment is in the form of Capture the Flag and should be individually solved by submitting the flag to the course [CTFd system](https://ctfd.bsy.fel.cvut.cz/).

- Each participants is assigned a docker container in the class infrastructure at the beginning of the class.
- Docker containers run Linux and contain all the tools needed during the course to solve all assignments.
- All assignments are to be done in the containers unless stated otherwise.

Starting dates and hard deadline dates are listed in the following table:
<!-- link assignment table from shared assets -->
{{< include-md file="partials/shared-block-assignments-table.md" >}}

### Assignment rules
<!-- link assignment table from shared assets -->
{{< include-md file="partials/shared-block-assignments-rules.md" >}}

## Final Exam
The exam takes place during the exam period in January/February. It consists of both theoretical and practical taks which are related to the content of the class and assignments. Participants are required to collect **at least** 50 points (out of 100) to pass the exam.

## Code of Conduct
<!-- link Code of conduct from shared assets-->
{{< include-md file="partials/shared-block-code-of-conduct.md" >}}

## Completion of the course
Participants who fulfill the requirements will receive an official Certificate of Completion issued by the [Czech Technical University in Prague](https://www.cvut.cz/en) (The [micro-credential](https://education.ec.europa.eu/education-levels/higher-education/micro-credentials) is valid and accepted across the European Union)   