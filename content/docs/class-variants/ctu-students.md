---
weight: 5
title: "CTU Students"
description: "Students enrolled in the courses of Czech Technical University in Prague"
icon: "school"
date: "2025-06-03T15:26:53+02:00"
lastmod: "2025-06-03T15:26:53+02:00"
draft: false
toc: true
---

## Class modality
<!-- This course teaches the fundamentals of cybersecurity through practical, hands-on experience in both attacking and defending. Students will perform penetration tests and learn to counter real attacks, alternating between offensive and defensive classes. Topics covered include reconnaissance, scanning, exploitation, privilege escalation, lateral movement, exfiltration, malware, network security forensics, binary reversing, log analysis, intrusion detection systems, honeypots, and basics of machine learning for security. By the end of the semester, students will be prepared for junior penetration tester roles or to continue as cybersecurity researchers and practitioners.

This course consists of weekly **3-hour blocks** which combine both theory and practical exercises. Students can attend the tutorials in person in room KN:E-107 or online via the [live stream](https://www.youtube.com/playlist?list=PLQL6z4JeTTQmu09ItEQaqjt6tk0KnRsLh) and Matrix communication platform. -->

<!-- link class description from shared assets-->
{{< include-md file="partials/shared-block-class-description.md" >}}

## Communication channels
In case you need to contact the teachers, there are two options:
1. Send an email to ALL teachers via *13136-bsy [at] fel.cvut.cz* (this way, any of them can answer), and always reply
to all
2. Contact us via class Matrix platform (Credentials are sent upon registration)

## Passing requirements
For successful class completion, students must complete a series of assignments (aka Zapocet) and the exam/bonus assignment.
- You have to be registered in [KOS](https://www.kos.cvut.cz/) to take this class.
- You need **30 points** (out of 50) from the assignments to get the Zápočet.
- Surplus over 40 points will improve your final grade after passing the exam/bonus assignment(up to 10 points).
- The bonus assignment is a special assignment given over Christmas that allows you to skip the exam if you have completed it.
- Both bonus assignments and exams include theoretical AND practical tasks
- Extra points can be awarded for:
    - Special Award for Services to the School - You can score extra points if you do something notable for the class or the class participants!

## Assignments
Several practical assignments are given during the course. Each assignment corresponds to a class and is designed to practice the content covered in the class. Each assignment is in the form of Capture the Flag and should be individually solved by submitting the flag to the course [CTFd system](https://ctfd.bsy.fel.cvut.cz/). In total, there are 50 points available for the assignments. Students are required to collect **at least 30 points** to get the assessment (Zápočet). Any surplus of over 40 points can be used to boost the final grade in the exam.

- Each student is assigned a docker container in the class infrastructure at the beginning of the class.
- Docker containers run Linux and contain all the tools needed during the semester to solve all assignments.
- All assignments are to be done in the containers unless stated otherwise.

Starting dates and hard deadline dates are listed in the following table:

<!-- link assignment table from shared assets -->
{{< include-md file="partials/shared-block-assignments-table.md" >}}

### Assignment rules
<!-- link assignment table from shared assets -->
{{< include-md file="partials/shared-block-assignments-rules.md" >}}

## Exam
The exam takes place during the exam period in January/February. It consists of both theoretical and practical taks which are related to the content of the class and assignments. Students are required to collect **at least** 50 points (out of 100) to pass the exam.

### Grading scale 
The final grade is computed using the standard CTU grading scale as follows:

```Exam score + surplus from assignments + any additional points = Total points```
| Total points | Grade |
| --------- | ----- |
| < 50 | F |
| 50 - 59 | E |
| 60 - 69 | D |
| 70 - 79 | C |
| 80 - 89 | B |
| 90+ | A |

![image](https://github.com/user-attachments/assets/d7245de2-0d0e-415d-9e2c-d47307f3b281)
{{< figure src="/images/bsy_point_requirements.png" width="80%" alt="Points from assignments and exam that are required for passing the course" >}}

### Bonus assignment
Bonus assignment will be announced before the winter break. Students who complete the bonus assignment can choose not to come to the final exam. More details and exact conditions for passing the bonus will be announced later in the semester.


## Code of Conduct
<!-- link Code of conduct from shared assets-->
{{< include-md file="partials/shared-block-code-of-conduct.md" >}}