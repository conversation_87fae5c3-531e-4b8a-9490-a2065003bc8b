---
weight: 3
title: "Free Tier Online Class"
description: "Students registered in the free online course"
icon: "public"
date: "2025-06-03T15:26:53+02:00"
lastmod: "2025-06-03T15:26:53+02:00"
draft: false
toc: true
---
## Class modality
<!-- link class description from shared assets-->
{{< include-md file="partials/shared-block-class-description.md" >}}

## Registration
Online enrollment for the class can be done through [Eventbrite](https://www.eventbrite.com/e/970324044337/).

## Communication channels
<!-- link communication channels from shared assets-->
{{< include-md file="partials/shared-block-communication-channels.md" >}}

## What are participants provided with?
- Detailed coursebook per class with commands and tools used
- Access to a chat platform to interact and ask questions
- [Live video stream](https://www.youtube.com/playlist?list=PLQL6z4JeTTQmu09ItEQaqjt6tk0KnRsLh) and [recordings](https://www.youtube.com/playlist?list=PLQL6z4JeTTQk_z3vwSIvn6wIHMeNQFU3d) of each lecture
- Cyber Range environment where to practice what is taught during class

## What do participants need for the classes?
- Computer to take the class and participate in hands-on exercise
- Good internet connection to access the class material and labs
- Docker installed to run a local Cyber Range
- Local Cyberrange insntalled (see [Cyber Range](#cyber-range))

## Passing requirements
- You have to be registered in [Eventbrite](https://www.eventbrite.com/e/970324044337/) to participate in the course.
- For participants registered in the free version of the course, there are no weekly assignments or exams. You can get a "Note of Completion" if you demonstrate class completion at the end of the course.
- To verify completion of the course, students are required to participate **in at least 12 out of 14 lessons**. The method of verification is introduced in the first lesson

## Cyber Range
To follow the class exercises and exercises, you need to use our [Cyber Range](https://github.com/stratosphereips/stratocyberlab) locally in your computer. It is a local, docker-based environment where you can *safely* practice both attacking and defensive techniques shown in the course. You **MUST** have it installed and working for the first class. So install it and try it now!

## Code of Conduct
<!-- link Code of conduct from shared assets-->
{{< include-md file="partials/shared-block-code-of-conduct.md" >}}

## Completion of the course
Participants who fulfill the requirements will receive a Note of Completion issued by the [Stratosphere Laboratory](https://www.stratosphereips.org/). If you are seeking official Certificate of Completion issued by the Czech Technical University (Certificate is accepted across the EU), see the [professional version] of this course.