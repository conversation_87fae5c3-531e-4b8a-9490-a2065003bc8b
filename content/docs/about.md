---
weight: 1
title: "Introduction to Security Course"
description: "Find answers to the core questions about the course"
icon: "target"
date: "2024-08-01T11:40:21+02:00"
lastmod: "2024-08-01T11:40:21+02:00"
draft: false
toc: true
---
The Introduction to Cybersecurity Master course is a 14-week intense hands-on course to foster knowledge about penetration testing and advanced cyber defenses. From basic security principles to advanced attacks, you will learn to attack in an isolated cyber range and learn how to detect and stop advanced intruders. The online version is free and gives you access to live classes, recordings, and a cyber range to practice what is taught during the lessons.


This course is part of the [Open Informatics Master](https://oi.fel.cvut.cz/en/master-program), taught at the Czech Technical University in Prague. The course is taught by members of the [Stratosphere Laboratory](https://www.stratosphereips.org/) in the [AI Center](https://www.aic.fel.cvut.cz/), [Computer Science Department](https://cs.fel.cvut.cz/), [Faculty of Electrical Engineering](https://fel.cvut.cz/cs), [Czech Technical University in Prague](https://cvut.cz/). 

## Who can take this course?

- Students interested in cybersecurity careers
- Individuals new to cybersecurity
- IT professionals seeking to expand their knowledge
- Anyone looking to expand their understanding of cyber threats and defenses

## Course Start Date 

This year there are 14 classes scheduled:
- The classes start on Thursday, September 25th, 2025.
- The classes end on Thursday, January 8th, 2025.

## Time Commitment

Students should allocate 6 hours per week for taking the course:
- The course consists of weekly 3-hour tutorials.
- Each tutorial is linked to a practical exercise in a cyber range, which takes an additional 2-3 hours per week.

## Prerequisites

- Time Commitment: 6 hours per week to dedicate the course
- English proficiency: a good understanding of the English language
- Learning attitude: a genuine desire to learn and have fun
- Have a basic understanding of how computer systems work
- Have a basic knowledge of Linux systems and command-line tools. You should be able to complete at least the first half of the Bandit challenge levels ([https://overthewire.org/wargames/bandit/](https://overthewire.org/wargames/bandit/)).
- Have a basic understanding of Python programming language

## How to take the course?
There are currently three ways of taking this course:
1. Free Online course: Free version of the course which includes not ony the weekly classes, study materials and recordings and online platform for communication. This option is ideal for anyone who wants to explore and study the current techniques used in cybersecurity. See [more details and registration](class-variants/free-tier.md) for the free online course.

2. Professional online course: Paid version of the course which includes not ony the weekly classes, study materials and recordings, but also **weekly hands-on assignments**, final exam, cloud-based cyberrange and official certificate of completion issued by the Czech Technical University in Prague. This opttion is ideal for professionals, seeking deep practical understanding of the principles of cybersecurity with internationally accepted certificate upon finishing. See [more details and registration](class-variants/professional-tier.md) for the deeper paid online course.

3. Students of the Czech Technical University in Prague: individuals enrolled as CTU Students have access full version of the course with the weekly hands-on assignments, final exam and cloud cyberrange for safe learning. See [more details and registration](class-variants/ctu-students.md)

## Course calendar
You can keep track of the lesson dates, times and topics using the public [google calendar](https://calendar.google.com/calendar/u/0?cid=***************************************************************************************************************************)

## Contact 

If you have any questions, contact us at 13136-bsy [at] fel.cvut.cz

## Code of Conduct
<!-- link Code of conduct from shared assets-->
{{< include-md file="partials/shared-block-code-of-conduct.md" >}}