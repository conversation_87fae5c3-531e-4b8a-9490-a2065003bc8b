---
weight: 6
title: "FAQ"
description: "Frequently Asked Questions"
icon: "help"
date: "2024-10-01T14:50:32+02:00"
lastmod: "2024-10-01T14:50:32+02:00"
draft: false
toc: true
---
## Who is this course for?
This course is designed for beginners to intermediate learners with a basic understanding of Linux and Python. Ideal for students, professionals, or anyone passionate about cybersecurity.

## What are the prerequisites?
~6 hours/week commitment, English proficiency, Basic knowledge of how computer systems work, Linux command-line tools (e.g., complete Bandit levels 1–15), Python programming, and knowing how to run Dockers on your system.

## Who teaches the course?
The course is taught by researchers and experts from the Stratosphere Laboratory at the AI Center, Faculty of Electrical Engineering, Czech Technical University in Prague.

## What’s the cyber range?
The cyber range is a Docker-based virtual lab where students safely practice hacking and defense techniques taught in class—locally on their own computers: [StratoCyberLab]https://github.com/stratosphereips/stratocyberlab

## What are the ways to take this course?
- [Free version](class-variants/free-tier.md) with full access to lessons and course books. 
- [CTU Enrolled Students](class-variants/ctu-students.md) – For degree-seeking students
- [Life-Long Learning (LLL) Students](class-variants/professional-tier.md) – Paid version with CTU-accredited certificate and EU validity

## What if I miss a class?
No problem! All classes are recorded and available to watch later, at your own pace. You can take the classes live or watch them later.

## How to access the class recordings?
The class recordings will be uploaded to the [YouTube Playlist](https://www.youtube.com/playlist?list=PLQL6z4JeTTQk_z3vwSIvn6wIHMeNQFU3d) between 24-48 hours after the class.

## How to access CTFd?
The [CTFd service](https://ctfd.bsy.fel.cvut.cz/login) is only available in the paid version of the course and the CTU students. If you are eligible for the access, you should have received an email with how to access it after registration.

## The StratoCyberLab is not working for me, how do i fix it?
1. Make sure your device has all the [requirements](https://github.com/stratosphereips/stratocyberlab?tab=readme-ov-file#requirements)
2. Make sure you pull the latest version of the StratoCyberLab repository with `git pull`
3. Check the [troubleshooting](https://github.com/stratosphereips/stratocyberlab?tab=readme-ov-file#troubleshooting) section of the StratoCyberLab readme
